# Leads Intake App Implementation

## Overview

This implementation replaces the original debug page with a full-stack leads intake application featuring:

- **Frontend**: React with TypeScript using company colors (#200529, #fff2e7, #fae8e0)
- **Backend**: Node.js BFF (Backend for Frontend) with Express
- **Authentication**: Integration with existing Azure AD/Easy Auth setup
- **Processing**: Async job processing with progress tracking

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React App     │    │   Node.js BFF   │    │ Leads Adapter   │
│   (Frontend)    │◄──►│   (Backend)     │◄──►│     API         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Features Implemented

### Frontend Components
- **Header**: User info display and sign-out functionality
- **File Upload**: Drag-and-drop file upload with validation (txt, csv, json, max 10MB)
- **Text Input**: Paste text area with keyboard shortcuts (Ctrl+Enter)
- **Progress Tracker**: Real-time progress updates with job status

### Backend Services
- **Authentication Middleware**: Validates Azure AD tokens and extracts user info
- **Leads Service**: Handles communication with leads-adapter-api
- **Job Management**: In-memory job tracking with progress updates
- **API Routes**: RESTful endpoints for processing and status checking

### Key Features
- **Real-time Progress**: Polling-based progress tracking
- **Error Handling**: Comprehensive error handling and user feedback
- **Responsive Design**: Mobile-friendly interface
- **Security**: Token validation and user-specific job access

## API Endpoints

### Authentication
- `GET /api/auth/user` - Get current user information
- `GET /api/auth/token` - Get current token (debug)

### Leads Processing
- `POST /api/leads/process` - Submit leads data for processing
- `GET /api/leads/status/:jobId` - Get job status and progress
- `GET /api/leads/history` - Get user's job history

## Development

### Prerequisites
- Node.js 20+
- npm

### Setup
```bash
npm install
npm run build
npm start
```

### Development Mode
```bash
# Run backend and frontend separately
npm run dev:full

# Or run individually
npm run dev          # Backend only
npm run dev:frontend # Frontend only
```

### Build for Production
```bash
npm run build
```

## Deployment

The application is containerized and ready for Azure Container Apps deployment:

```bash
docker build -t leads-intake-app .
docker run -p 8080:8080 leads-intake-app
```

## Environment Variables

- `AAD_TENANT_ID` - Azure AD tenant ID
- `AAD_CLIENT_ID` - Azure AD client ID  
- `LEADS_ADAPTER_API_URL` - URL of the leads adapter API
- `PORT` - Server port (default: 8080)

## Integration with Leads Adapter API

The BFF communicates with the leads-adapter-api via HTTPS REST calls:

```javascript
POST /api/process
{
  "content": "leads data...",
  "type": "text|file",
  "fileName": "optional.txt",
  "metadata": {
    "jobId": "uuid",
    "user": { "name": "...", "email": "..." },
    "timestamp": "2024-09-10T..."
  }
}
```

## Security

- Azure AD token validation
- User-specific job access control
- Input validation and sanitization
- File type and size restrictions
- CORS protection

## Next Steps

1. **Database Integration**: Replace in-memory job storage with persistent database
2. **WebSocket Support**: Real-time updates instead of polling
3. **File Storage**: Integrate with Azure Blob Storage for large files
4. **Monitoring**: Add application insights and logging
5. **Testing**: Add unit and integration tests
