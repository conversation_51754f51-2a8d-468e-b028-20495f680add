import React, { useState, useRef, DragEvent } from "react"
import { ProcessLeadsRequest } from "../types"

interface LeadsProcessorProps {
  onProcess: (request: ProcessLeadsRequest) => void
  disabled: boolean
}

const LeadsProcessor: React.FC<LeadsProcessorProps> = ({
  onProcess,
  disabled,
}) => {
  const [isDragOver, setIsDragOver] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [text, setText] = useState("")
  const [activeTab, setActiveTab] = useState<"text" | "file">("text")
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDragOver = (e: DragEvent) => {
    e.preventDefault()
    if (!disabled) {
      setIsDragOver(true)
    }
  }

  const handleDragLeave = (e: DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)

    if (disabled) return

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
      setActiveTab("file")
    }
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleFileSelect = (file: File) => {
    // Check file type and size
    const allowedTypes = [
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/msword",
      "text/plain",
    ]
    const maxSize = 10 * 1024 * 1024 // 10MB

    if (
      !allowedTypes.includes(file.type) &&
      !file.name.match(/\.(pdf|docx|doc|txt)$/i)
    ) {
      alert("Please select a valid file type (PDF, DOCX, DOC, or TXT)")
      return
    }

    if (file.size > maxSize) {
      alert("File size must be less than 10MB")
      return
    }

    setSelectedFile(file)
    setText("") // Clear text when file is selected
  }

  const handleUploadClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value)
    if (e.target.value.trim()) {
      setSelectedFile(null) // Clear file when text is entered
    }
  }

  const handleProcess = async () => {
    if (disabled) return

    if (activeTab === "text" && text.trim()) {
      onProcess({
        content: text.trim(),
        type: "text",
      })
      setText("")
    } else if (activeTab === "file" && selectedFile) {
      try {
        const content = await readFileContent(selectedFile)
        onProcess({
          content,
          type: "file",
          fileName: selectedFile.name,
        })
        setSelectedFile(null)
        if (fileInputRef.current) {
          fileInputRef.current.value = ""
        }
      } catch (error) {
        alert("Failed to read file content")
      }
    }
  }

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result
        if (typeof result === "string") {
          resolve(result)
        } else if (result instanceof ArrayBuffer) {
          // For binary files (PDF, DOCX), convert to base64
          const base64 = btoa(String.fromCharCode(...new Uint8Array(result)))
          resolve(base64)
        } else {
          reject(new Error("Unexpected file content type"))
        }
      }
      reader.onerror = () => {
        reject(new Error("Failed to read file"))
      }

      // Read as text for TXT files, as array buffer for binary files
      if (file.type === "text/plain" || file.name.match(/\.txt$/i)) {
        reader.readAsText(file)
      } else {
        reader.readAsArrayBuffer(file)
      }
    })
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.ctrlKey && e.key === "Enter") {
      handleProcess()
    }
  }

  const canProcess =
    (activeTab === "file" && selectedFile) ||
    (activeTab === "text" && text.trim())

  return (
    <div className="leads-processor">
      <div className="tab-container">
        <button
          className={`tab-button ${activeTab === "text" ? "active" : ""}`}
          onClick={() => setActiveTab("text")}
          disabled={disabled}
        >
          📝 Paste Text
        </button>
        <button
          className={`tab-button ${activeTab === "file" ? "active" : ""}`}
          onClick={() => setActiveTab("file")}
          disabled={disabled}
        >
          📄 Upload File
        </button>
      </div>

      <div className="tab-content">
        {activeTab === "text" ? (
          <div className="text-section">
            <textarea
              className="text-area"
              placeholder="Paste your leads data here..."
              value={text}
              onChange={handleTextChange}
              onKeyDown={handleKeyDown}
              disabled={disabled}
            />
            <div
              style={{ fontSize: "0.9rem", color: "#666", marginTop: "0.5rem" }}
            >
              Tip: Press Ctrl+Enter to process quickly
            </div>
          </div>
        ) : (
          <div className="file-section">
            <div
              className={`upload-area ${isDragOver ? "drag-over" : ""}`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={handleUploadClick}
            >
              <div className="upload-icon">📄</div>
              <div className="upload-text">
                {selectedFile
                  ? selectedFile.name
                  : "Drag and drop a file here, or click to select"}
              </div>
              <div className="upload-subtext">
                Supported: PDF, DOCX, DOC, TXT (max 10MB)
              </div>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              className="file-input"
              accept=".pdf,.docx,.doc,.txt,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/msword,text/plain"
              onChange={handleFileInputChange}
              disabled={disabled}
            />
          </div>
        )}
      </div>

      <div className="process-section">
        <button
          className="process-btn primary"
          onClick={handleProcess}
          disabled={disabled || !canProcess}
        >
          {disabled ? (
            <>
              <span className="loading"></span>
              Processing...
            </>
          ) : (
            <>🚀 Process {activeTab === "file" ? "File" : "Text"}</>
          )}
        </button>
      </div>
    </div>
  )
}

export default LeadsProcessor
