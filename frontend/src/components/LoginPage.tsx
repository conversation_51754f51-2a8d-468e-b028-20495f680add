import React, { useState } from 'react';

interface LoginPageProps {
  error?: string | null;
}

const LoginPage: React.FC<LoginPageProps> = ({ error }) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSignIn = () => {
    setIsLoading(true);
    // In production, this redirects to Azure AD
    window.location.href = '/.auth/login/aad';
  };

  const handleDevelopmentLogin = () => {
    // For local development, simulate login with mock user
    if (process.env.NODE_ENV === 'development') {
      const mockUser = {
        name: 'Mock User',
        email: email || '<EMAIL>',
        roles: ['user']
      };
      
      // Store mock user in localStorage for development
      localStorage.setItem('mockUser', JSON.stringify(mockUser));
      window.location.reload();
    }
  };

  const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <div className="nion-logo">✱ nion</div>
          <h1 className="login-title">Sign in</h1>
        </div>

        <div className="login-form">
          <input
            type="email"
            className="login-input"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          
          {error && (
            <div className="login-error">
              {error}
            </div>
          )}

          <button
            className="login-button"
            onClick={handleSignIn}
            disabled={isLoading}
          >
            {isLoading ? 'Signing in...' : 'Next'}
          </button>

          <div className="login-help">
            <a href="#" className="login-link">
              Can't access your account?
            </a>
          </div>
        </div>

        <div className="login-options">
          <button className="login-options-button">
            <span className="login-options-icon">🔑</span>
            Sign-in options
          </button>
        </div>

        {isDevelopment && (
          <div className="dev-login">
            <hr className="dev-divider" />
            <p className="dev-text">Development Mode</p>
            <button
              className="dev-button"
              onClick={handleDevelopmentLogin}
            >
              Mock Login for Testing
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoginPage;
