import { useState, useEffect } from "react"
import Header from "./components/Header"
import LeadsProcessor from "./components/LeadsProcessor"
import ProgressTracker from "./components/ProgressTracker"
import LoginPage from "./components/LoginPage"
import { User, ProcessingJob, ProcessLeadsRequest } from "./types"
import { authService } from "./services/authService"
import { apiService } from "./services/apiService"
import "./styles/index.css"

function App() {
  const [user, setUser] = useState<User | null>(null)
  const [currentJob, setCurrentJob] = useState<ProcessingJob | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [authError, setAuthError] = useState<string | null>(null)

  useEffect(() => {
    // Get user info from authentication headers
    authService
      .getCurrentUser()
      .then((user) => {
        setUser(user)
        setIsLoading(false)
      })
      .catch((err) => {
        setAuthError(err.message || "Authentication failed")
        setIsLoading(false)
      })
  }, [])

  const handleSignOut = () => {
    authService.signOut()
  }

  const handleProcessLeads = async (request: ProcessLeadsRequest) => {
    if (isProcessing) return

    setIsProcessing(true)
    setError(null)
    setCurrentJob(null)

    try {
      const response = await apiService.processLeads(request)

      // Start polling for job status
      const jobId = response.jobId
      pollJobStatus(jobId)
    } catch (err: any) {
      setError(err.message || "Failed to process leads")
      setIsProcessing(false)
    }
  }

  const pollJobStatus = async (jobId: string) => {
    try {
      const job = await apiService.getJobStatus(jobId)
      setCurrentJob(job)

      if (job.status === "processing" || job.status === "pending") {
        setTimeout(() => pollJobStatus(jobId), 2000)
      } else {
        setIsProcessing(false)
      }
    } catch (err: any) {
      setError(err.message || "Failed to get job status")
      setIsProcessing(false)
    }
  }

  if (isLoading) {
    return (
      <div className="app">
        <div className="main-content">
          <div className="content-header">
            <h1 className="content-title">Loading...</h1>
            <p className="content-subtitle">Authenticating user</p>
          </div>
        </div>
      </div>
    )
  }

  if (authError || !user) {
    return <LoginPage error={authError} />
  }

  return (
    <div className="app">
      <Header user={user} onSignOut={handleSignOut} />

      <main className="main-content">
        <div className="content-header">
          <h1 className="content-title">Process Leads Data</h1>
          <p className="content-subtitle">
            Upload a file or paste text to process leads and call-off requests
          </p>
        </div>

        {error && <div className="status-message status-error">{error}</div>}

        <div className="process-container">
          <LeadsProcessor
            onProcess={handleProcessLeads}
            disabled={isProcessing}
          />
        </div>

        {(currentJob || isProcessing) && (
          <ProgressTracker job={currentJob} isProcessing={isProcessing} />
        )}
      </main>
    </div>
  )
}

export default App
