import { User } from "../types"

class AuthService {
  async getCurrentUser(): Promise<User> {
    // Force development mode for localhost
    const isLocalhost =
      window.location.hostname === "localhost" ||
      window.location.hostname === "127.0.0.1" ||
      window.location.href.includes("localhost:8080")

    // ALWAYS use mock user for localhost
    if (isLocalhost) {
      // Auto-create a default mock user for development
      const defaultMockUser = {
        name: "Development User",
        email: "<EMAIL>",
        roles: ["user"],
      }
      localStorage.setItem("mockUser", JSON.stringify(defaultMockUser))
      return defaultMockUser
    }

    try {
      const response = await fetch("/api/auth/user", {
        method: "GET",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        // In development, if auth fails, throw a specific error
        if (this.isDevelopment()) {
          throw new Error("Please use mock login for local development")
        }

        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || "Authentication required")
      }

      const userData = await response.json()
      return userData
    } catch (error) {
      // In development, if there's a network error, assume we need mock login
      if (this.isDevelopment() && (error as any).name === "TypeError") {
        throw new Error("Please use mock login for local development")
      }

      throw error
    }
  }

  private isDevelopment(): boolean {
    return (
      window.location.hostname === "localhost" ||
      window.location.hostname === "127.0.0.1" ||
      window.location.hostname.includes("localhost") ||
      window.location.port === "8080" ||
      window.location.port === "3000"
    )
  }

  async getToken(): Promise<string | null> {
    try {
      const response = await fetch("/api/auth/token", {
        method: "GET",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        return null
      }

      const tokenData = await response.json()
      return tokenData.token
    } catch (error) {
      return null
    }
  }

  signOut(): void {
    // Check if we're in development (localhost)
    const isLocalhost =
      window.location.hostname === "localhost" ||
      window.location.hostname === "127.0.0.1" ||
      window.location.href.includes("localhost:8080")

    // Clear mock user in development
    if (isLocalhost) {
      localStorage.removeItem("mockUser")
      window.location.reload()
      return
    }

    window.location.href = "/.auth/logout"
  }
}

export const authService = new AuthService()
