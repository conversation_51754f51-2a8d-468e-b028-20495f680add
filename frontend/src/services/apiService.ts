import {
  ProcessLeadsRequest,
  ProcessLeadsResponse,
  ProcessingJob,
  ApiError,
} from "../types"
import { mockLeadsService } from "./mockLeadsService"

class ApiService {
  private baseUrl = "/api"
  private isDevelopment =
    window.location.hostname === "localhost" ||
    window.location.hostname === "127.0.0.1"

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`

    const response = await fetch(url, {
      ...options,
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    })

    if (!response.ok) {
      let errorMessage = "An error occurred"
      try {
        const errorData = await response.json()
        errorMessage = errorData.message || errorMessage
      } catch {
        errorMessage = response.statusText || errorMessage
      }

      const error: ApiError = {
        message: errorMessage,
        code: response.status.toString(),
      }
      throw error
    }

    return response.json()
  }

  async processLeads(
    request: ProcessLeadsRequest
  ): Promise<ProcessLeadsResponse> {
    if (this.isDevelopment) {
      return mockLeadsService.processLeads(
        request.content,
        request.type,
        request.fileName
      )
    }

    return this.request<ProcessLeadsResponse>("/leads/process", {
      method: "POST",
      body: JSON.stringify(request),
    })
  }

  async getJobStatus(jobId: string): Promise<ProcessingJob> {
    if (this.isDevelopment) {
      const mockJob = await mockLeadsService.getJobStatus(jobId)
      if (!mockJob) {
        const error: ApiError = { message: "Job not found", code: "404" }
        throw error
      }
      return mockJob as ProcessingJob
    }

    return this.request<ProcessingJob>(`/leads/status/${jobId}`)
  }

  async getJobHistory(): Promise<ProcessingJob[]> {
    if (this.isDevelopment) {
      // Return empty array for mock - could be enhanced later
      return []
    }

    return this.request<ProcessingJob[]>("/leads/history")
  }
}

export const apiService = new ApiService()
