// <PERSON>ck leads service for development
export interface ProcessLeadsResponse {
  jobId: string;
  message: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
}

export interface JobStatus {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  message: string;
  result?: any;
  createdAt: string;
  updatedAt: string;
}

class MockLeadsService {
  private jobs: Map<string, JobStatus> = new Map();

  async processLeads(content: string, type: 'text' | 'file', fileName?: string): Promise<ProcessLeadsResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const jobId = `mock-job-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const job: JobStatus = {
      id: jobId,
      status: 'pending',
      progress: 0,
      message: 'Job created successfully',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.jobs.set(jobId, job);

    // Simulate processing in background
    this.simulateProcessing(jobId, content, type, fileName);

    return {
      jobId,
      message: 'Processing started successfully',
      status: 'pending'
    };
  }

  async getJobStatus(jobId: string): Promise<JobStatus | null> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200));
    
    return this.jobs.get(jobId) || null;
  }

  private async simulateProcessing(jobId: string, content: string, type: 'text' | 'file', fileName?: string) {
    const job = this.jobs.get(jobId);
    if (!job) return;

    // Update to processing
    job.status = 'processing';
    job.progress = 10;
    job.message = 'Analyzing content...';
    job.updatedAt = new Date().toISOString();

    // Simulate progress updates
    const progressSteps = [
      { progress: 25, message: 'Extracting leads data...' },
      { progress: 50, message: 'Validating lead information...' },
      { progress: 75, message: 'Processing leads...' },
      { progress: 90, message: 'Finalizing results...' }
    ];

    for (const step of progressSteps) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      job.progress = step.progress;
      job.message = step.message;
      job.updatedAt = new Date().toISOString();
    }

    // Complete the job
    await new Promise(resolve => setTimeout(resolve, 1000));
    job.status = 'completed';
    job.progress = 100;
    job.message = 'Processing completed successfully';
    job.updatedAt = new Date().toISOString();
    
    // Mock result based on content type
    if (type === 'file') {
      job.result = {
        fileName: fileName || 'unknown.file',
        fileType: this.getFileType(fileName),
        contentLength: content.length,
        leadsFound: Math.floor(Math.random() * 50) + 10,
        validLeads: Math.floor(Math.random() * 40) + 8,
        duplicates: Math.floor(Math.random() * 5),
        errors: Math.floor(Math.random() * 3)
      };
    } else {
      job.result = {
        contentType: 'text',
        contentLength: content.length,
        leadsFound: Math.floor(Math.random() * 20) + 5,
        validLeads: Math.floor(Math.random() * 18) + 4,
        duplicates: Math.floor(Math.random() * 2),
        errors: Math.floor(Math.random() * 2)
      };
    }
  }

  private getFileType(fileName?: string): string {
    if (!fileName) return 'unknown';
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'pdf': return 'PDF Document';
      case 'docx': return 'Word Document (DOCX)';
      case 'doc': return 'Word Document (DOC)';
      case 'txt': return 'Text File';
      default: return 'Unknown';
    }
  }
}

export const mockLeadsService = new MockLeadsService();
