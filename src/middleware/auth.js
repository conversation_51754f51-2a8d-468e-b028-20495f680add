import jwt from "jsonwebtoken"
import jwksClient from "jwks-rsa"

function decodeClientPrincipal(headerValue) {
  try {
    if (!headerValue) return null
    const buf = Buffer.from(headerValue, "base64")
    const json = buf.toString("utf8")
    return JSON.parse(json)
  } catch (e) {
    return {
      error: "Failed to decode X-MS-CLIENT-PRINCIPAL",
      details: String(e),
    }
  }
}

function getIdTokenFromRequest(req) {
  const h = req.headers
  return (
    h["x-ms-token-aad-id-token"] ||
    h["x-ms-token-aad-idtoken"] ||
    h["x-ms-token-aad-id-token".toLowerCase()] ||
    null
  )
}

function getTenantIdFromEnvOrToken(decoded) {
  const envTid =
    process.env.AAD_TENANT_ID ||
    process.env.TENANT_ID ||
    process.env.AZURE_TENANT_ID
  if (envTid) return envTid
  return decoded?.tid || decoded?.tenant || null
}

function makeJwksClientForTenant(tenantId) {
  const jwksUri = `https://login.microsoftonline.com/${tenantId}/discovery/v2.0/keys`
  return jwksClient({
    jwksUri,
    cache: true,
    cacheMaxEntries: 5,
    cacheMaxAge: 10 * 60 * 1000,
    rateLimit: true,
    jwksRequestsPerMinute: 10,
    timeout: 8000,
  })
}

async function verifyIdToken(idToken, opts = {}) {
  if (!idToken) return { ok: false, reason: "No token provided" }
  const decoded = jwt.decode(idToken, { complete: true })
  if (!decoded) return { ok: false, reason: "Token could not be decoded" }

  const claims = decoded.payload || {}
  const kid = decoded.header?.kid
  const tenantId = getTenantIdFromEnvOrToken(claims)
  if (!tenantId) return { ok: false, reason: "Tenant ID not found" }

  const audience =
    process.env.AAD_CLIENT_ID ||
    process.env.CLIENT_ID ||
    opts.audience ||
    claims.aud
  const issuer = `https://login.microsoftonline.com/${tenantId}/v2.0`

  const client = makeJwksClientForTenant(tenantId)
  const getKey = (header, cb) => {
    const kidToUse = header.kid || kid
    client.getSigningKey(kidToUse, (err, key) => {
      if (err) return cb(err)
      const signingKey = key.getPublicKey()
      cb(null, signingKey)
    })
  }

  return new Promise((resolve) => {
    jwt.verify(
      idToken,
      getKey,
      {
        algorithms: ["RS256"],
        audience,
        issuer,
      },
      (err, verified) => {
        if (err) return resolve({ ok: false, reason: String(err), claims })
        resolve({ ok: true, claims: verified, issuer, audience })
      }
    )
  })
}

export const authMiddleware = async (req, res, next) => {
  try {
    // Debug logging
    console.log("Auth middleware - Headers:", {
      "x-ms-client-principal": !!req.headers["x-ms-client-principal"],
      "x-ms-token-aad-id-token": !!req.headers["x-ms-token-aad-id-token"],
      "x-ms-token-aad-idtoken": !!req.headers["x-ms-token-aad-idtoken"],
      "user-agent": req.headers["user-agent"],
      path: req.path,
    })

    // Get client principal from Easy Auth
    const principalHeader = req.headers["x-ms-client-principal"]
    const principal = decodeClientPrincipal(principalHeader)

    // Get ID token
    let idToken = getIdTokenFromRequest(req)

    // For development, try environment variable
    if (!idToken && process.env.AUTH_ID_TOKEN) {
      idToken = process.env.AUTH_ID_TOKEN
    }

    if (!idToken) {
      console.log("Auth middleware - No token found, principal:", principal)
      return res.status(401).json({
        message: "No authentication token found",
        error: "MISSING_TOKEN",
        debug: {
          hasPrincipal: !!principal,
          principalError: principal?.error,
          headers: Object.keys(req.headers).filter((h) =>
            h.startsWith("x-ms-")
          ),
        },
      })
    }

    // Verify the token
    const verification = await verifyIdToken(idToken)

    if (!verification.ok) {
      console.log(
        "Auth middleware - Token verification failed:",
        verification.reason
      )
      return res.status(401).json({
        message: "Invalid authentication token",
        error: "INVALID_TOKEN",
        reason: verification.reason,
      })
    }

    // Add user info to request
    req.user = {
      name:
        verification.claims.name || principal?.userDetails || "Unknown User",
      email:
        verification.claims.email ||
        verification.claims.preferred_username ||
        "<EMAIL>",
      roles: principal?.userRoles || [],
      claims: verification.claims,
      principal,
    }

    req.token = idToken

    console.log("Auth middleware - Success for user:", req.user.email)
    next()
  } catch (error) {
    console.error("Auth middleware error:", error)
    res.status(500).json({
      message: "Authentication error",
      error: "AUTH_ERROR",
      details: error.message,
    })
  }
}

export { decodeClientPrincipal, getIdTokenFromRequest, verifyIdToken }
