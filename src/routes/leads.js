import express from "express"
import { authMiddleware } from "../middleware/auth.js"
import { leadsService } from "../services/leadsService.js"

const router = express.Router()

// Apply authentication middleware to all routes
router.use(authMiddleware)

// Process leads data
router.post("/process", async (req, res) => {
  try {
    console.log(
      "Leads process endpoint - User:",
      req.user?.email,
      "Roles:",
      req.user?.roles
    )
    console.log(
      "Leads process endpoint - Request body keys:",
      Object.keys(req.body)
    )

    const { content, type, fileName } = req.body

    // Validate input
    if (!content || !content.trim()) {
      return res.status(400).json({
        message: "Content is required",
        error: "MISSING_CONTENT",
      })
    }

    if (!type || !["text", "file"].includes(type)) {
      return res.status(400).json({
        message: 'Type must be either "text" or "file"',
        error: "INVALID_TYPE",
      })
    }

    // Check content length (10MB limit)
    if (content.length > 10 * 1024 * 1024) {
      return res.status(400).json({
        message: "Content too large (max 10MB)",
        error: "CONTENT_TOO_LARGE",
      })
    }

    console.log(
      "Leads process endpoint - Starting processing for user:",
      req.user.email
    )

    // Start processing
    const result = await leadsService.processLeads(
      content,
      type,
      fileName,
      req.user
    )

    console.log(
      "Leads process endpoint - Processing started, jobId:",
      result.jobId
    )
    res.json(result)
  } catch (error) {
    console.error("Error processing leads:", error)
    res.status(500).json({
      message: "Failed to process leads",
      error: "PROCESSING_ERROR",
      details: error.message,
    })
  }
})

// Get job status
router.get("/status/:jobId", (req, res) => {
  try {
    const { jobId } = req.params

    if (!jobId) {
      return res.status(400).json({
        message: "Job ID is required",
        error: "MISSING_JOB_ID",
      })
    }

    const job = leadsService.getJob(jobId)

    if (!job) {
      return res.status(404).json({
        message: "Job not found",
        error: "JOB_NOT_FOUND",
      })
    }

    // Check if user has access to this job
    if (job.user.email !== req.user.email) {
      return res.status(403).json({
        message: "Access denied",
        error: "ACCESS_DENIED",
      })
    }

    // Return job without sensitive data
    const publicJob = {
      id: job.id,
      status: job.status,
      progress: job.progress,
      message: job.message,
      result: job.result,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
    }

    res.json(publicJob)
  } catch (error) {
    console.error("Error getting job status:", error)
    res.status(500).json({
      message: "Failed to get job status",
      error: "STATUS_ERROR",
      details: error.message,
    })
  }
})

// Get job history for current user
router.get("/history", (req, res) => {
  try {
    const jobs = leadsService.getUserJobs(req.user.email)

    // Return jobs without sensitive data
    const publicJobs = jobs.map((job) => ({
      id: job.id,
      status: job.status,
      progress: job.progress,
      message: job.message,
      type: job.type,
      fileName: job.fileName,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
    }))

    res.json(publicJobs)
  } catch (error) {
    console.error("Error getting job history:", error)
    res.status(500).json({
      message: "Failed to get job history",
      error: "HISTORY_ERROR",
      details: error.message,
    })
  }
})

export default router
