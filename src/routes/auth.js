import express from "express"
import { authMiddleware } from "../middleware/auth.js"

const router = express.Router()

// Get current user information
router.get("/user", authMiddleware, (req, res) => {
  try {
    const user = {
      name: req.user.name,
      email: req.user.email,
      roles: req.user.roles,
    }

    res.json(user)
  } catch (error) {
    console.error("Error getting user info:", error)
    res.status(500).json({
      message: "Failed to get user information",
      error: "USER_INFO_ERROR",
    })
  }
})

// Get current token (for debugging purposes)
router.get("/token", authMiddleware, (req, res) => {
  try {
    res.json({
      token: req.token,
      hasToken: !!req.token,
    })
  } catch (error) {
    console.error("Error getting token:", error)
    res.status(500).json({
      message: "Failed to get token information",
      error: "TOKEN_ERROR",
    })
  }
})

// Development mock login endpoint
router.post("/mock-login", (req, res) => {
  if (process.env.NODE_ENV !== "development") {
    return res.status(403).json({
      message: "Mock login only available in development mode",
      error: "NOT_DEVELOPMENT",
    })
  }

  const { email, name } = req.body

  const mockUser = {
    name: name || "Mock User",
    email: email || "<EMAIL>",
    roles: ["user"],
  }

  res.json(mockUser)
})

export default router
