import axios from "axios"
import { v4 as uuidv4 } from "uuid"

// In-memory job storage (in production, use a database)
const jobs = new Map()

class LeadsService {
  constructor() {
    this.leadsAdapterUrl =
      process.env.LEADS_ADAPTER_API_URL ||
      "https://leads-intake-app.greensea-2681856e.swedencentral.azurecontainerapps.io/"
    this.timeout = 30000
  }

  async processLeads(content, type, fileName, user) {
    const jobId = uuidv4()

    // Create initial job record
    const job = {
      id: jobId,
      status: "pending",
      progress: 0,
      message: "Job created, waiting to start processing...",
      content,
      type,
      fileName,
      user: {
        name: user.name,
        email: user.email,
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    jobs.set(jobId, job)

    // Start processing asynchronously
    this.startProcessing(jobId).catch((error) => {
      console.error(`Error processing job ${jobId}:`, error)
      this.updateJob(jobId, {
        status: "failed",
        progress: 0,
        message: error.message || "Processing failed",
        updatedAt: new Date().toISOString(),
      })
    })

    return { jobId, message: "Processing started" }
  }

  async startProcessing(jobId) {
    const job = jobs.get(jobId)
    if (!job) {
      throw new Error("Job not found")
    }

    try {
      // Update job status to processing
      this.updateJob(jobId, {
        status: "processing",
        progress: 10,
        message: "Sending data to leads adapter...",
        updatedAt: new Date().toISOString(),
      })

      // Prepare the request to leads-adapter-api
      const requestData = {
        content: job.content,
        type: job.type,
        fileName: job.fileName,
        metadata: {
          jobId,
          user: job.user,
          timestamp: new Date().toISOString(),
        },
      }

      // Update progress
      this.updateJob(jobId, {
        progress: 30,
        message: "Processing data...",
        updatedAt: new Date().toISOString(),
      })

      // Make request to leads-adapter-api
      const response = await axios.post(
        `${this.leadsAdapterUrl}/api/process`,
        requestData,
        {
          timeout: this.timeout,
          headers: {
            "Content-Type": "application/json",
            "User-Agent": "leads-intake-app/1.0",
          },
        }
      )

      // Update progress
      this.updateJob(jobId, {
        progress: 80,
        message: "Processing completed, finalizing results...",
        updatedAt: new Date().toISOString(),
      })

      // Simulate some final processing time
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Complete the job
      this.updateJob(jobId, {
        status: "completed",
        progress: 100,
        message: "Processing completed successfully",
        result: response.data,
        updatedAt: new Date().toISOString(),
      })
    } catch (error) {
      console.error(`Processing error for job ${jobId}:`, error)

      let errorMessage = "Processing failed"
      if (error.code === "ECONNREFUSED") {
        errorMessage = "Unable to connect to leads adapter service"
      } else if (error.code === "ENOTFOUND") {
        errorMessage = "Leads adapter service not found"
      } else if (error.response) {
        errorMessage =
          error.response.data?.message ||
          `HTTP ${error.response.status}: ${error.response.statusText}`
      } else if (error.message) {
        errorMessage = error.message
      }

      this.updateJob(jobId, {
        status: "failed",
        progress: 0,
        message: errorMessage,
        error: {
          code: error.code,
          message: error.message,
          status: error.response?.status,
        },
        updatedAt: new Date().toISOString(),
      })
    }
  }

  updateJob(jobId, updates) {
    const job = jobs.get(jobId)
    if (job) {
      Object.assign(job, updates)
      jobs.set(jobId, job)
    }
  }

  getJob(jobId) {
    return jobs.get(jobId) || null
  }

  getUserJobs(userEmail) {
    return Array.from(jobs.values())
      .filter((job) => job.user.email === userEmail)
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  }

  // Cleanup old jobs (call this periodically)
  cleanupOldJobs(maxAgeHours = 24) {
    const cutoff = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000)

    for (const [jobId, job] of jobs.entries()) {
      if (new Date(job.createdAt) < cutoff) {
        jobs.delete(jobId)
      }
    }
  }
}

export const leadsService = new LeadsService()

// Cleanup old jobs every hour
setInterval(() => {
  leadsService.cleanupOldJobs()
}, 60 * 60 * 1000)
