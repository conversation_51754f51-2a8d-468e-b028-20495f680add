{"name": "leads-intake-app", "version": "0.1.0", "private": true, "description": "Leads intake application with React frontend and Node.js BFF", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "NODE_ENV=development nodemon --watch src src/index.js", "build": "npm run build:frontend", "build:frontend": "vite build", "dev:frontend": "vite", "dev:full": "concurrently \"npm run dev\" \"npm run dev:frontend\""}, "dependencies": {"express": "^4.19.2", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "multer": "^1.4.5-lts.1", "axios": "^1.6.0", "cors": "^2.8.5", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.1.4", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.2.2", "vite": "^5.0.8", "concurrently": "^8.2.2"}}