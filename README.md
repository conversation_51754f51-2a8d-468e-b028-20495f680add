# Leads Intake App

A full-stack leads intake application built for Azure Container Apps with Easy Auth integration.

## Features

- **Frontend**: React with TypeScript
- **Backend**: Node.js BFF (Backend for Frontend) with Express
- **Authentication**: Azure AD/Entra ID integration with Easy Auth
- **File Processing**: Plain text and file (pdf/docx/doc/txt) upload and processing
- **Progress Tracking**: Real-time job status monitoring
- **Development Support**: Mock authentication for local development

## Local development

- **Requirements**: Node.js 20+
- **Install**: `npm install`
- **Start**: `npm start`
- **Open**: http://localhost:8080

### Development Authentication

The app automatically uses mock authentication when running on localhost. No additional setup required for basic development.

### Optional: Testing with Real Azure AD Tokens

For testing token verification locally:

- Set `AAD_TENANT_ID` and `AAD_CLIENT_ID` environment variables
- Provide an ID token via either:
  - `AUTH_ID_TOKEN` environment variable, or
  - `.dev/id_token.txt` file (not committed)

### Build Commands

- **Frontend only**: `npm run build:frontend`
- **Full build**: `npm run build` (builds frontend and copies to dist/)

## Docker

Build locally with auto-detected tool (docker/podman/nerdctl):

```
make build IMAGE_TAG=dev
make run IMAGE_TAG=dev
```

Or with explicit registry:

```
make build REGISTRY=myacr.azurecr.io IMAGE_TAG=$(git rev-parse --short HEAD)
make push REGISTRY=myacr.azurecr.io IMAGE_TAG=$(git rev-parse --short HEAD)
```

## Azure DevOps pipeline

`azure-pipelines.yaml` builds on push to `main`, pushes to ACR, deploys/updates the Container App, and configures Easy Auth.

Set these pipeline variables (Library or pipeline variables):

- `azureServiceConnection`: Service connection name with access to the subscription
- `location`: Azure region (e.g. westeurope)
- `acrName`: Existing Azure Container Registry name (e.g. nionacrshared)
- `containerAppsEnv`: Existing Container Apps Environment name
- `tenantId`: Your Microsoft Entra tenant ID (single-tenant)
- `tokenStoreSasUrl`: SAS URL to a blob container (for token store)

Notes:

- The pipeline creates or reuses an Entra app with display name "Leads Intake", and always creates a fresh client secret (secrets cannot be retrieved). It then stores the secret in the Container App as `ms-provider-client-secret` and wires auth.
- Redirect URI is set to `https://<app-fqdn>/.auth/login/aad/callback` after the app is (re)deployed so the FQDN is known.

## Custom domain (nionit.com)

Recommended approach for ACA:

1. Decide a subdomain, e.g. `leads-intake-app.dev.nionit.com`.
2. Add a CNAME in your DNS (nameSRS registrar) pointing the subdomain to the app’s default FQDN shown in `az containerapp show` (e.g. `leads-intake-app.<hash>.<region>.azurecontainerapps.io`).
3. Validate domain and bind certificate:
   - Option A: Use your own certificate (upload via `az containerapp hostname bind` referencing a Key Vault cert or PEM)
   - Option B: Use Azure-managed certificates (requires Azure DNS zone; with external registrar, managed certs are not available—bring your own cert)
4. Add the custom domain in ACA: `az containerapp hostname add -g <rg> -n leads-intake-app --hostname leads-intake-app.dev.nionit.com`
5. Update the Entra app redirect URIs to include `https://leads-intake-app.dev.nionit.com/.auth/login/aad/callback` (pipeline script can append once DNS/cert is in place).

If you need apex domain support, use ALIAS/ANAME at DNS provider or front with Azure Front Door. CNAME is recommended for subdomains.

## ACA Auth expectations

- Provider: Microsoft (Entra) with single-tenant audience (AzureADMyOrg)
- Allowed audience: the created app’s client ID (default verifier checks `aud`)
- ID tokens enabled, token store backed by Blob Storage (SAS URL configured via pipeline variable)
- Only users from your Entra tenant can sign in
