trigger:
  branches:
    include:
      - main

pr: none

variables:
  # Service connection to use for AzureCLI@2
  azureServiceConnection: 'azure-build-connection'

  # Naming
  appName: 'leads-intake-app'
  displayName: 'Leads Intake'

  # Environment mapping
  environmentName: 'dev'
  resourceGroup: 'rg-$(environmentName)'
  location: 'swedencentral'

  # Container Apps environment (must exist)
  containerAppsEnv: 'aca-env-dev'

  # Runtime managed identity to attach to the Container App for ACR pulls
  runtimeManagedIdentityName: 'id-aca-$(environmentName)'

  # Registry
  acrName: 'acrcinodehelper'

  # Optionally provide Entra secret to avoid rotation
  entraClientSecret: ''

  # Use existing Entra app registration (preferred; no directory write perms required)
  # Provide the application (client) ID via variable or store in Key Vault as 'ms-provider-client-id'
  aadAppClientId: ''

  # Token store blob SAS URL (set in variable group or library). If empty, read from KV.
  tokenStoreSasUrl: ''

  # Dapr configuration
  # App ID for this component when Dapr sidecar is enabled
  daprAppId: '$(appName)'
  # Optional downstream service app-id to be invoked via Dapr
  downstreamAppId: 'leads-adapter-api'
  # Max request body size (MB) for Dapr HTTP/GRPC server
  daprHttpMaxRequestSizeMb: '15'

stages:
  - stage: BuildAndPush
    displayName: Build and push image
    jobs:
      - job: build
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - checkout: self

          - task: AzureCLI@2
            displayName: 'Build and push image with Docker Buildx + registry cache'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              scriptType: bash
              scriptLocation: inlineScript
              inlineScript: |
                set -euxo pipefail
                # Ensure Container Apps extension available
                az extension add --name containerapp --upgrade -y 1>/dev/null || true
                if [[ -z "$(acrName)" ]]; then echo "acrName variable is required" >&2; exit 1; fi

                # Login to ACR using Azure CLI (AAD token, no admin creds)
                az acr login -n $(acrName)
                ACR_LOGIN_SERVER=$(az acr show -n $(acrName) --query loginServer -o tsv)

                # Prepare tags
                IMAGE_SHA_TAG="$ACR_LOGIN_SERVER/$(appName):$(Build.SourceVersion)"
                IMAGE_LATEST_TAG="$ACR_LOGIN_SERVER/$(appName):latest"

                # Setup Buildx builder (if missing)
                docker buildx inspect builder >/dev/null 2>&1 || docker buildx create --name builder --use
                docker buildx use builder
                docker buildx inspect --bootstrap

                # Use ACR-backed cache to persist across hosted agents
                CACHE_REF="$ACR_LOGIN_SERVER/cache/$(appName):buildcache"
                docker buildx build \
                  --tag "$IMAGE_SHA_TAG" \
                  --tag "$IMAGE_LATEST_TAG" \
                  --cache-from type=registry,ref="$CACHE_REF" \
                  --cache-to type=registry,mode=max,ref="$CACHE_REF" \
                  --push \
                  -f Dockerfile \
                  .

  - stage: Deploy
    displayName: Deploy to $(environmentName)
    dependsOn: BuildAndPush
    jobs:
      - job: deploy
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - checkout: self

          - task: AzureCLI@2
            displayName: 'Deploy Container App and configure auth'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              scriptType: bash
              scriptLocation: inlineScript
              inlineScript: |
                set -euxo pipefail

                RG=$(resourceGroup)
                APP=$(appName)
                CAE=$(containerAppsEnv)
                LOCATION=$(location)

                if [[ -z "$CAE" ]]; then
                  echo "containerAppsEnv variable is required" >&2
                  exit 1
                fi

                # Ensure Container Apps extension available on this job runner
                az extension add --name containerapp --upgrade -y 1>/dev/null || true

                # Ensure resource group exists
                az group create -n "$RG" -l "$LOCATION" 1>/dev/null

                # Resolve ACR login server (use managed identity for pulls; no admin creds)
                ACR_LOGIN_SERVER=$(az acr show -n $(acrName) --query loginServer -o tsv)
                IMAGE="$ACR_LOGIN_SERVER/$(appName):$(Build.SourceVersion)"

                # Resolve user-assigned managed identity for runtime (pulling from ACR)
                UAMI_NAME=$(runtimeManagedIdentityName)
                UAMI_ID=$(az identity show -g "$RG" -n "$UAMI_NAME" --query id -o tsv)

                # Compose Dapr and env-var settings
                DAPR_APP_ID=$(daprAppId)
                DOWNSTREAM_APP_ID=$(downstreamAppId)
                DAPR_ENV_ARGS=(
                  --set-env-vars "DAPR_HTTP_PORT=3500"
                  --set-env-vars "DAPR_HTTP_ENDPOINT=http://127.0.0.1:3500"
                )
                if [[ -n "$DOWNSTREAM_APP_ID" ]]; then
                  DAPR_ENV_ARGS+=(
                    --set-env-vars "DOWNSTREAM_APP_ID=$DOWNSTREAM_APP_ID"
                    --set-env-vars "DOWNSTREAM_BASE_URL_DAPR=http://127.0.0.1:3500/v1.0/invoke/$DOWNSTREAM_APP_ID/method"
                  )
                fi

                # Decide create vs update and enforce KV credentials on first creation
                if az containerapp show -g "$RG" -n "$APP" 1>/dev/null 2>&1; then
                  # Ensure identity attached, then configure registry identity before updating image
                  az containerapp identity assign -g "$RG" -n "$APP" --user-assigned "$UAMI_ID" 1>/dev/null
                  az containerapp registry set -g "$RG" -n "$APP" \
                    --server "$ACR_LOGIN_SERVER" \
                    --identity "$UAMI_ID" 1>/dev/null
                  az containerapp update -g "$RG" -n "$APP" \
                    --image "$IMAGE" \
                    "${DAPR_ENV_ARGS[@]}" 1>/dev/null
                  # Ensure Dapr is enabled/updated for this app
                  az containerapp dapr enable -g "$RG" -n "$APP" \
                    --dapr-app-id "$DAPR_APP_ID" \
                    --dapr-app-port 8080 \
                    --dapr-http-max-request-size "$(daprHttpMaxRequestSizeMb)" 1>/dev/null
                else
                  # First-time creation requires Entra credentials present in KV
                  if [[ -z "$APP_ID" || -z "$CLIENT_SECRET_VALUE" ]]; then
                    echo "First-time Container App creation requires Entra app credentials in Key Vault: ms-provider-client-id and ms-provider-client-secret in $VAULT." >&2
                    exit 1
                  fi
                  az containerapp create -g "$RG" -n "$APP" \
                    --environment "$CAE" \
                    --ingress external --target-port 8080 \
                    --min-replicas 1 --max-replicas 1 \
                    --image "$IMAGE" \
                    --registry-server "$ACR_LOGIN_SERVER" \
                    --user-assigned "$UAMI_ID" \
                    --registry-identity "$UAMI_ID" 1>/dev/null
                  # Set env vars after creation
                  az containerapp update -g "$RG" -n "$APP" \
                    "${DAPR_ENV_ARGS[@]}" 1>/dev/null
                  # Enable Dapr with app id/port
                  az containerapp dapr enable -g "$RG" -n "$APP" \
                    --dapr-app-id "$DAPR_APP_ID" \
                    --dapr-app-port 8080 \
                    --dapr-http-max-request-size "$(daprHttpMaxRequestSizeMb)" 1>/dev/null
                fi

                # Get FQDN to set redirect URI
                FQDN=$(az containerapp show -g "$RG" -n "$APP" --query properties.configuration.ingress.fqdn -o tsv)
                REDIRECT_URI="https://${FQDN}/.auth/login/aad/callback"

                # Use existing Entra application defined by variable or Key Vault
                TENANT_ID=$(az account show --query tenantId -o tsv)
                VAULT="kv-leads-intake-$(environmentName)"

                # Resolve client secret from Key Vault (optional; if absent we reuse the existing Container App secret configured by admins)
                CLIENT_SECRET_VALUE=$(az keyvault secret show --vault-name "$VAULT" --name ms-provider-client-secret --query value -o tsv || true)

                # Resolve APP_ID from variable, KV, or AAD (display name) — without modifying AAD
                APP_ID="$(aadAppClientId)"
                if [[ -z "$APP_ID" ]]; then
                  APP_ID=$(az keyvault secret show --vault-name "$VAULT" --name ms-provider-client-id --query value -o tsv || true)
                fi
                if [[ -z "$APP_ID" ]]; then
                  APP_ID=$(az ad app list --display-name "$(displayName)" --query "[0].appId" -o tsv || true)
                fi
                if [[ -z "$APP_ID" ]]; then
                  echo "Unable to resolve AAD app client ID. Provide pipeline variable 'aadAppClientId' or store 'ms-provider-client-id' in Key Vault $VAULT, or ensure an Entra app named '$(displayName)' exists." >&2
                  exit 1
                fi

                # Do NOT modify Entra app configuration here (redirect URIs, secrets, etc.)
                # Admins manage the app. Pipeline only reads values and configures ACA.

                # Configure Easy Auth for Container App
                chmod +x scripts/configure-aca-auth.sh
                # Resolve Token Store SAS URL (required for ID token header availability)
                TOKENSTORE_SAS="$(tokenStoreSasUrl)"
                if [[ -z "$TOKENSTORE_SAS" ]]; then
                  TOKENSTORE_SAS=$(az keyvault secret show --vault-name "$VAULT" --name easyauth-tokenstore-sas --query value -o tsv || true)
                fi
                if [[ -z "$TOKENSTORE_SAS" ]]; then
                  echo "Token store SAS URL is required (set pipeline var tokenStoreSasUrl or KV secret 'easyauth-tokenstore-sas' in $VAULT)." >&2
                  exit 1
                fi

                export APP_NAME="$APP" RESOURCE_GROUP="$RG" CLIENT_ID="$APP_ID" CLIENT_SECRET="$CLIENT_SECRET_VALUE" TENANT_ID="$TENANT_ID" TOKENSTORE_SAS_URL="$TOKENSTORE_SAS"
                scripts/configure-aca-auth.sh

          - task: AzureCLI@2
            displayName: 'Show auth config'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              scriptType: bash
              scriptLocation: inlineScript
              inlineScript: |
                az containerapp auth show -g $(resourceGroup) -n $(appName)
