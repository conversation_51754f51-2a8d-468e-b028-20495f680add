# syntax=docker/dockerfile:1

FROM node:20-alpine AS base
WORKDIR /app
ENV NODE_ENV=production \
    PORT=8080

# Build stage for frontend
FROM base AS frontend-builder
COPY package.json package-lock.json ./
COPY frontend/ ./frontend/
COPY vite.config.ts ./
RUN npm install
RUN npm run build

# Dependencies stage
FROM base AS deps
COPY package.json package-lock.json ./
RUN npm install --omit=dev

# Final runtime stage
FROM base AS runner
# Install tini in the final image (required by ENTRYPOINT)
RUN apk add --no-cache --update tini \
    && addgroup -S app && adduser -S app -G app
USER app
COPY --from=deps /app/node_modules ./node_modules
COPY --from=frontend-builder /app/dist ./dist
COPY src ./src
EXPOSE 8080
ENTRYPOINT ["/sbin/tini", "--"]
CMD ["node", "src/index.js"]
